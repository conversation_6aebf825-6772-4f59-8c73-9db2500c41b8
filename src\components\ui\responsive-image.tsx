'use client'

import Image, { ImageProps } from 'next/image'
import { useState } from 'react'
import { cn } from '@/lib/utils'
import { PlaceholderImage } from './placeholder-image'

interface ResponsiveImageProps extends Omit<ImageProps, 'onLoad' | 'onError'> {
  fallbackSrc?: string | null
  aspectRatio?: string
  containerClassName?: string
  showPlaceholder?: boolean
}

export function ResponsiveImage({
  src,
  alt,
  width,
  height,
  fallbackSrc = null,
  aspectRatio = 'aspect-video',
  containerClassName,
  className,
  showPlaceholder = true,
  ...props
}: ResponsiveImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  return (
    <div
      className={cn(
        'overflow-hidden relative',
        aspectRatio,
        containerClassName
      )}
    >
      {hasError && !fallbackSrc && showPlaceholder ? (
        <PlaceholderImage />
      ) : (
        <>
          <Image
            src={hasError && fallbackSrc ? fallbackSrc : src}
            alt={alt}
            width={width}
            height={height}
            className={cn(
              'object-cover transition-all duration-300 w-full h-full',
              isLoading ? 'scale-110 blur-sm' : 'scale-100 blur-0',
              className
            )}
            onLoadingComplete={() => setIsLoading(false)}
            onError={() => {
              setHasError(true)
              setIsLoading(false)
            }}
            {...props}
          />
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted/20">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
            </div>
          )}
        </>
      )}
    </div>
  )
}
