"use client"

import * as React from "react"
import {
  IconCamera,
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFileAi,
  IconFileDescription,
  IconFileWord,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/admin-dashboard/nav-documents"
import { NavMain } from "@/components/admin-dashboard/nav-main"
import { NavSecondary } from "@/components/admin-dashboard/nav-secondary"
import { NavUser } from "@/components/admin-dashboard/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard/admin-dashboard",
      icon: IconDashboard,
    },
    {
      title: "Project Management",
      url: "/dashboard/admin-dashboard/projects",
      icon: IconFolder,
    },
    {
      title: "Client Management",
      url: "/dashboard/admin-dashboard/clients",
      icon: IconUsers,
    },
    {
      title: "Website Analytics",
      url: "/dashboard/admin-dashboard/analytics",
      icon: IconChartBar,
    },
    {
      title: "Communications",
      url: "/dashboard/admin-dashboard/messages",
      icon: IconListDetails,
    },
  ],
  navClouds: [
    {
      title: "Project Operations",
      icon: IconFolder,
      isActive: true,
      url: "/dashboard/admin-dashboard/projects",
      items: [
        {
          title: "Create New Project",
          url: "/dashboard/admin-dashboard/projects/new",
        },
        {
          title: "Update Progress",
          url: "/dashboard/admin-dashboard/projects/updates",
        },
        {
          title: "Project Archive",
          url: "/dashboard/admin-dashboard/projects/archive",
        },
      ],
    },
    {
      title: "Client Communications",
      icon: IconFileDescription,
      url: "/dashboard/admin-dashboard/messages",
      items: [
        {
          title: "All Messages",
          url: "/dashboard/admin-dashboard/messages",
        },
        {
          title: "Urgent Requests",
          url: "/dashboard/admin-dashboard/messages/urgent",
        },
        {
          title: "Project Discussions",
          url: "/dashboard/admin-dashboard/messages/projects",
        },
      ],
    },
    {
      title: "Website Monitoring",
      icon: IconFileAi,
      url: "/dashboard/admin-dashboard/monitoring",
      items: [
        {
          title: "Activity Monitor",
          url: "/dashboard/admin-dashboard/monitoring/activity",
        },
        {
          title: "Performance Metrics",
          url: "/dashboard/admin-dashboard/monitoring/performance",
        },
        {
          title: "User Analytics",
          url: "/dashboard/admin-dashboard/monitoring/users",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: IconSettings,
    },
    {
      title: "Get Help",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "Search",
      url: "#",
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: "Project Database",
      url: "/dashboard/admin-dashboard/database",
      icon: IconDatabase,
    },
    {
      name: "Analytics Reports",
      url: "/dashboard/admin-dashboard/reports",
      icon: IconReport,
    },
    {
      name: "Client Documents",
      url: "/dashboard/admin-dashboard/documents",
      icon: IconFileWord,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">Your Company</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
