import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generatePasswordResetToken } from '@/lib/auth'
import { z } from 'zod'

const requestResetSchema = z.object({
  email: z.string().email('Invalid email address'),
})

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validatedData = requestResetSchema.parse(body)

    // Generate reset token
    const resetToken = await generatePasswordResetToken(validatedData.email)

    if (!resetToken) {
      // Don't reveal whether the email exists or not
      return NextResponse.json({
        message: 'If an account exists with this email, you will receive a password reset link'
      })
    }

    // TODO: In a real application, send this token via email
    // For development, we'll return it in the response
    return NextResponse.json({
      message: 'If an account exists with this email, you will receive a password reset link',
      // Remove this in production:
      debug: {
        resetToken
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.issues[0].message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}