"use client"

import * as React from "react"
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  Pie<PERSON>hart,
  Settings2,
  SquareTerminal,
} from "lucide-react"

import { NavMain } from "@/components/client-dashboard/nav-main"
import { NavProjects } from "@/components/client-dashboard/nav-projects"
import { NavUser } from "@/components/client-dashboard/nav-user"
import { TeamSwitcher } from "@/components/client-dashboard/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Acme Inc",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: Command,
      plan: "Free",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard/client-dashboard",
      icon: SquareTerminal,
      isActive: true,
    },
    {
      title: "My Projects",
      url: "/dashboard/client-dashboard/projects",
      icon: Bot,
      items: [
        {
          title: "Active Projects",
          url: "/dashboard/client-dashboard/projects/active",
        },
        {
          title: "Completed Projects",
          url: "/dashboard/client-dashboard/projects/completed",
        },
        {
          title: "Project Archive",
          url: "/dashboard/client-dashboard/projects/archive",
        },
      ],
    },
    {
      title: "Project Preview",
      url: "/dashboard/client-dashboard/preview",
      icon: BookOpen,
      items: [
        {
          title: "Live Previews",
          url: "/dashboard/client-dashboard/preview/live",
        },
        {
          title: "Development Stages",
          url: "/dashboard/client-dashboard/preview/stages",
        },
        {
          title: "Version History",
          url: "/dashboard/client-dashboard/preview/history",
        },
      ],
    },
    {
      title: "Collaboration",
      url: "/dashboard/client-dashboard/collaboration",
      icon: Settings2,
      items: [
        {
          title: "Project Discussions",
          url: "/dashboard/client-dashboard/collaboration/discussions",
        },
        {
          title: "Feedback & Reviews",
          url: "/dashboard/client-dashboard/collaboration/feedback",
        },
        {
          title: "File Sharing",
          url: "/dashboard/client-dashboard/collaboration/files",
        },
        {
          title: "Team Communication",
          url: "/dashboard/client-dashboard/collaboration/team",
        },
      ],
    },
  ],
  projects: [
    {
      name: "E-commerce Website",
      url: "/dashboard/client-dashboard/projects/ecommerce",
      icon: Frame,
    },
    {
      name: "Portfolio Website",
      url: "/dashboard/client-dashboard/projects/portfolio",
      icon: PieChart,
    },
    {
      name: "Corporate Website",
      url: "/dashboard/client-dashboard/projects/corporate",
      icon: Map,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
