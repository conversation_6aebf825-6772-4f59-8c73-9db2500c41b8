// File path: src/components/sections/about-faq.tsx
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const faqs = [
  {
    question: "How do I collaborate on a project?",
    answer: "You can start by signing up through our collaboration portal. Our team will review your application and reach out within 48 hours."
  },
  {
    question: "What industries do you serve?",
    answer: "We work across multiple sectors including FinTech, Healthcare, E-commerce, and Education, bringing innovative solutions to each industry."
  },
  // Add more FAQs
]

export function AboutFAQ() {
  return (
    <section className="py-24 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12">
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-6">About Us</h2>
            <p className="text-muted-foreground mb-4">
              Founded in 2023, we bridge gaps in tech by developing innovative solutions 
              that address modern challenges. Our dual approach of creating our own products 
              while offering expert services allows us to stay at the forefront of technology 
              while helping others achieve their vision.
            </p>
          </div>
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-6">FAQs</h2>
            <Accordion type="single" collapsible>
              {faqs.map((faq, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger>{faq.question}</AccordionTrigger>
                  <AccordionContent>{faq.answer}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </div>
    </section>
  )
}