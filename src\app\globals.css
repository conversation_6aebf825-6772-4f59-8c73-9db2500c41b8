@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.147 0.004 49.25);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.147 0.004 49.25);
  --primary: oklch(0.55 0.25 260); /* Rich purple */
  --primary-foreground: oklch(0.985 0.001 106.423);
  --secondary: oklch(0.98 0.02 105);
  --secondary-foreground: oklch(0.216 0.006 56.043);
  --muted: oklch(0.97 0.001 106.424);
  --muted-foreground: oklch(0.553 0.013 58.071);
  --accent: oklch(0.7 0.3 190); /* Vibrant teal */
  --accent-foreground: oklch(0.985 0.001 106.423);
  --destructive: oklch(0.7 0.3 30); /* Bright red */
  --border: oklch(0.923 0.003 48.717);
  --input: oklch(0.923 0.003 48.717);
  --ring: oklch(0.55 0.25 260); /* Match primary */
  --chart-1: oklch(0.7 0.3 40); /* Orange */
  --chart-2: oklch(0.6 0.2 180); /* Teal */
  --chart-3: oklch(0.5 0.2 250); /* Blue (primary) */
  --chart-4: oklch(0.6 0.25 300); /* Purple */
  --chart-5: oklch(0.65 0.25 25); /* Red */
  --sidebar: oklch(0.985 0.001 106.423);
  --sidebar-foreground: oklch(0.147 0.004 49.25);
  --sidebar-primary: oklch(0.5 0.2 250); /* Match primary */
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.97 0.001 106.424);
  --sidebar-accent-foreground: oklch(0.216 0.006 56.043);
  --sidebar-border: oklch(0.923 0.003 48.717);
  --sidebar-ring: oklch(0.5 0.2 250); /* Match primary */
  --background: oklch(1 0 0);
  --foreground: oklch(0.147 0.004 49.25);
}

.dark {
  --background: oklch(0.1 0.02 270); /* Deep purple-black background */
  --foreground: oklch(0.985 0.001 106.423);
  --card: oklch(0.14 0.02 270); /* Slightly lighter than background */
  --card-foreground: oklch(0.985 0.001 106.423);
  --popover: oklch(0.14 0.02 270); /* Match card */
  --popover-foreground: oklch(0.985 0.001 106.423);
  --primary: oklch(0.75 0.25 260); /* Bright purple */
  --primary-foreground: oklch(0.1 0.02 270); /* Match background */
  --secondary: oklch(0.18 0.02 270); /* Darker than card */
  --secondary-foreground: oklch(0.985 0.001 106.423);
  --muted: oklch(0.18 0.02 270); /* Match secondary */
  --muted-foreground: oklch(0.75 0.05 270); /* Subtle text */
  --accent: oklch(0.75 0.3 190); /* Bright teal accent */
  --accent-foreground: oklch(0.1 0.02 270); /* Match background */
  --destructive: oklch(0.75 0.3 30); /* Bright red */
  --destructive-foreground: oklch(0.985 0.001 106.423);
  --border: oklch(1 0 0 / 20%); /* More visible borders */
  --input: oklch(1 0 0 / 20%);
  --ring: oklch(0.75 0.25 260); /* Match primary */
  --chart-1: oklch(0.75 0.3 40); /* Orange */
  --chart-2: oklch(0.75 0.3 180); /* Teal */
  --chart-3: oklch(0.75 0.25 260); /* Purple (primary) */
  --chart-4: oklch(0.75 0.25 300); /* Violet */
  --chart-5: oklch(0.75 0.3 30); /* Red */
  --sidebar: oklch(0.14 0.02 270); /* Match card */
  --sidebar-foreground: oklch(0.985 0.001 106.423);
  --sidebar-primary: oklch(0.75 0.25 260); /* Match primary */
  --sidebar-primary-foreground: oklch(0.1 0.02 270); /* Match background */
  --sidebar-accent: oklch(0.18 0.02 270); /* Match secondary */
  --sidebar-accent-foreground: oklch(0.985 0.001 106.423);
  --sidebar-border: oklch(1 0 0 / 20%); /* Match border */
  --sidebar-ring: oklch(0.75 0.25 260); /* Match primary */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Progress Bar Animation */
@keyframes progress {
  from {
    width: 0;
  }
}

.progress-bar {
  animation: progress 1s ease-out forwards;
}

/* Custom animation delay classes */
.animation-delay-0 {
  animation-delay: 0ms;
}

.animation-delay-150 {
  animation-delay: 150ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}
