'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function DashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Get user role from localStorage and redirect to appropriate dashboard
    const role = localStorage.getItem('userRole')
    
    if (role === 'admin') {
      router.push('/dashboard/admin-dashboard')
    } else if (role === 'client') {
      router.push('/dashboard/client-dashboard')
    } else {
      // No role found, redirect to login
      router.push('/login')
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Redirecting to your dashboard...</p>
      </div>
    </div>
  )
}
