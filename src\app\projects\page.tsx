// File path: src/app/projects/page.tsx
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ProjectCard } from "@/components/products/project-card"
import Link from "next/link"

const projects = [
  {
    name: "Project Alpha",
    description: "An AI-powered analytics platform that helps businesses understand customer behavior and make data-driven decisions. Built with modern technologies for maximum performance and scalability.",
    techStack: ["React", "TypeScript", "Python", "TensorFlow", "AWS"],
    githubUrl: "https://github.com/yourcompany/project-alpha",
    status: {
      type: "completed" as const,
      version: "1.0",
    },
  },
  {
    name: "Project Beta",
    description: "Next-generation collaboration tools for remote teams, featuring real-time communication, project management, and AI-assisted workflow optimization.",
    techStack: ["Next.js", "Node.js", "WebRTC", "PostgreSQL", "Docker"],
    status: {
      type: "in_progress" as const,
      progress: 60,
      launchDate: new Date("2025-08-01"),
    },
  },
  {
    name: "Project Gamma",
    description: "Open-source blockchain infrastructure for secure, decentralized data management and smart contract execution.",
    techStack: ["Rust", "Solidity", "WebAssembly", "GraphQL"],
    githubUrl: "https://github.com/yourcompany/project-gamma",
    status: {
      type: "in_progress" as const,
      progress: 85,
      launchDate: new Date("2025-06-15"),
    },
  }
]

export default function ProjectsPage() {
  return (
    <main className="min-h-screen py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight mb-6">
            Our Projects
          </h1>
          <p className="text-xl text-muted-foreground">
            At Your Company, we build tools to solve modern challenges. 
            Explore our open-source and proprietary projects below.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {projects.map((project) => (
            <ProjectCard key={project.name} project={project} />
          ))}
        </div>

        <div className="max-w-xl mx-auto text-center space-y-6">
          <Button asChild size="lg" className="w-full sm:w-auto">
            <Link href="/contact?type=project">
              Have a project idea? Let's discuss
            </Link>
          </Button>
          <div>
            <Link 
              href="/contact?type=contribute" 
              className="text-primary hover:underline text-sm"
            >
              Interested in contributing to our open-source projects?
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
  )
}