// File path: src/components/products/project-card.tsx
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Github, ExternalLink } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface ProjectCardProps {
  project: {
    name: string
    description: string
    techStack: string[]
    githubUrl?: string
    status: {
      type: 'completed' | 'in_progress'
      version?: string
      progress?: number
      launchDate?: Date
    }
  }
}

export function ProjectCard({ project }: ProjectCardProps) {
  const isCompleted = project.status.type === 'completed'

  return (
    <Card className="flex flex-col h-full group hover:shadow-xl transition-all duration-300 overflow-hidden">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between text-xl">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent group-hover:from-accent group-hover:to-primary transition-all duration-500">
            {project.name}
          </span>
          {project.githubUrl && (
            <Link
              href={project.githubUrl}
              target="_blank"
              className="text-muted-foreground hover:text-primary transition-colors"
            >
              <Github className="h-5 w-5" />
            </Link>
          )}
        </CardTitle>
        <CardDescription className="text-base line-clamp-3">{project.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow pb-6">
        <div className="space-y-5">
          <div className="flex flex-wrap gap-2">
            {project.techStack.map((tech) => (
              <span
                key={tech}
                className="px-3 py-1 text-xs font-medium rounded-full bg-primary/10 text-primary border border-primary/10 transition-colors hover:bg-primary/20"
              >
                {tech}
              </span>
            ))}
          </div>
          <div className={cn(
            "mt-4 text-sm font-medium p-3 rounded-lg border",
            isCompleted
              ? "text-green-600 bg-green-50 border-green-100 dark:bg-green-950/20 dark:border-green-900/30"
              : "text-amber-600 bg-amber-50 border-amber-100 dark:bg-amber-950/20 dark:border-amber-900/30"
          )}>
            {isCompleted ? (
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                Version {project.status.version} Available
              </div>
            ) : (
              <div>
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-amber-500 mr-2"></div>
                  In Development: {project.status.progress}% complete
                </div>
                {project.status.launchDate && (
                  <div className="text-muted-foreground mt-2 flex items-center">
                    <div className="h-1.5 w-1.5 rounded-full bg-muted-foreground/50 mr-2"></div>
                    Launching: {project.status.launchDate.toLocaleDateString()}
                  </div>
                )}
                <div className="w-full h-1.5 bg-amber-200 rounded-full mt-2 dark:bg-amber-900/30">
                  <div
                    className={`h-full bg-amber-500 rounded-full w-[${project.status.progress}%]`}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col gap-4 sm:flex-row pt-0">
        <Button
          asChild
          variant={isCompleted ? "default" : "gradient"}
          size="lg"
          className="w-full sm:w-auto rounded-lg"
        >
          {isCompleted ? (
            <Link href="#">Download v{project.status.version}</Link>
          ) : (
            <Link href="#">Learn More</Link>
          )}
        </Button>
        <Button
          variant="outline"
          asChild
          className="w-full sm:w-auto rounded-lg"
        >
          <Link href="/contact?type=collaboration">
            Collaborate with Us
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}