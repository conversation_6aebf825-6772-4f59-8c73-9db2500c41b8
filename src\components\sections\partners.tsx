// File path: src/components/sections/partners.tsx
export function Partners() {
  return (
    <section className="py-24">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold tracking-tight text-center mb-12">
          Trusted By
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 items-center justify-items-center">
          {/* Replace with actual partner logos */}
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="bg-slate-100 rounded-lg p-8 w-full aspect-video flex items-center justify-center">
              <span className="text-muted-foreground">Your Logo Here</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}