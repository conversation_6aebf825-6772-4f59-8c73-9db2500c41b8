'use client'

import { useState } from 'react'
import { ClientSidebar } from "@/components/client-dashboard/client-sidebar"
import { <PERSON><PERSON><PERSON>ead<PERSON> } from "@/components/client-dashboard/client-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  SidebarInset, 
  SidebarProvider 
} from "@/components/ui/sidebar"
import { 
  Eye, 
  MessageSquare, 
  Users, 
  Calendar,
  ExternalLink,
  GitBranch,
  Clock,
  DollarSign
} from "lucide-react"

// Mock data - replace with real API calls
const mockProjects = [
  {
    id: '1',
    name: 'E-commerce Website',
    description: 'Modern e-commerce platform with payment integration and inventory management',
    status: 'IN_PROGRESS',
    progress: 65,
    startDate: '2024-01-15',
    expectedEndDate: '2024-03-15',
    previewUrl: 'https://preview.example.com/project1',
    repositoryUrl: 'https://github.com/company/project1',
    budget: 5000,
    lastUpdate: '2024-01-20',
    features: ['Payment Integration', 'Inventory Management', 'User Authentication', 'Admin Dashboard']
  },
  {
    id: '2',
    name: 'Portfolio Website',
    description: 'Personal portfolio with blog functionality and contact forms',
    status: 'REVIEW',
    progress: 90,
    startDate: '2023-12-01',
    expectedEndDate: '2024-01-30',
    previewUrl: 'https://preview.example.com/project2',
    repositoryUrl: 'https://github.com/company/project2',
    budget: 2500,
    lastUpdate: '2024-01-18',
    features: ['Blog System', 'Contact Forms', 'SEO Optimization', 'Responsive Design']
  },
  {
    id: '3',
    name: 'Corporate Website',
    description: 'Professional corporate website with CMS integration',
    status: 'COMPLETED',
    progress: 100,
    startDate: '2023-10-01',
    expectedEndDate: '2023-12-15',
    actualEndDate: '2023-12-10',
    previewUrl: 'https://preview.example.com/project3',
    repositoryUrl: 'https://github.com/company/project3',
    budget: 7500,
    lastUpdate: '2023-12-10',
    features: ['CMS Integration', 'Multi-language Support', 'Analytics', 'Contact Management']
  }
]

export default function ProjectsPage() {
  const [activeTab, setActiveTab] = useState('all')

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNING': return 'bg-blue-500'
      case 'IN_PROGRESS': return 'bg-yellow-500'
      case 'REVIEW': return 'bg-purple-500'
      case 'TESTING': return 'bg-orange-500'
      case 'COMPLETED': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS': return 'In Progress'
      case 'REVIEW': return 'Under Review'
      default: return status.charAt(0) + status.slice(1).toLowerCase()
    }
  }

  const filterProjects = (status: string) => {
    if (status === 'all') return mockProjects
    if (status === 'active') return mockProjects.filter(p => ['PLANNING', 'IN_PROGRESS', 'REVIEW', 'TESTING'].includes(p.status))
    if (status === 'completed') return mockProjects.filter(p => p.status === 'COMPLETED')
    return mockProjects.filter(p => p.status === status.toUpperCase())
  }

  const filteredProjects = filterProjects(activeTab)

  return (
    <SidebarProvider>
      <ClientSidebar />
      <SidebarInset>
        <ClientHeader userName="Client" unreadMessages={2} />
        
        <main className="flex-1 p-6 space-y-6">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">My Projects</h1>
            <p className="text-muted-foreground">
              Monitor the progress of your projects and collaborate with our development team.
            </p>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList>
              <TabsTrigger value="all">All Projects</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-6">
              <div className="grid gap-6">
                {filteredProjects.map((project) => (
                  <Card key={project.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-3">
                            <CardTitle className="text-xl">{project.name}</CardTitle>
                            <Badge className={getStatusColor(project.status)}>
                              {getStatusText(project.status)}
                            </Badge>
                          </div>
                          <CardDescription className="text-base">{project.description}</CardDescription>
                        </div>
                        <div className="text-right space-y-1">
                          <p className="text-sm text-muted-foreground">Budget</p>
                          <p className="text-lg font-semibold">${project.budget.toLocaleString()}</p>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-6">
                      {/* Progress */}
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span className="font-medium">Progress</span>
                          <span className="text-muted-foreground">{project.progress}% Complete</span>
                        </div>
                        <Progress value={project.progress} className="h-3" />
                      </div>

                      {/* Project Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="space-y-1">
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Start Date
                          </p>
                          <p className="font-medium">{new Date(project.startDate).toLocaleDateString()}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Expected End
                          </p>
                          <p className="font-medium">{new Date(project.expectedEndDate).toLocaleDateString()}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <GitBranch className="h-3 w-3" />
                            Last Update
                          </p>
                          <p className="font-medium">{new Date(project.lastUpdate).toLocaleDateString()}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            Budget
                          </p>
                          <p className="font-medium">${project.budget.toLocaleString()}</p>
                        </div>
                      </div>

                      {/* Features */}
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Key Features</p>
                        <div className="flex flex-wrap gap-2">
                          {project.features.map((feature, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-wrap gap-2">
                        <Button size="sm" variant="default">
                          <Eye className="h-4 w-4 mr-2" />
                          Preview Website
                        </Button>
                        <Button size="sm" variant="outline">
                          <Users className="h-4 w-4 mr-2" />
                          Collaborate
                        </Button>
                        <Button size="sm" variant="outline">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Messages
                        </Button>
                        <Button size="sm" variant="outline">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Repository
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredProjects.length === 0 && (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <GitBranch className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No projects found</h3>
                    <p className="text-muted-foreground text-center">
                      {activeTab === 'all' 
                        ? "You don't have any projects yet. Contact us to get started!"
                        : `No ${activeTab} projects found.`
                      }
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
