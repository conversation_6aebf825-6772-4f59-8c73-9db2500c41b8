// File path: src/components/sections/testimonials.tsx
'use client'

import * as React from "react"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

const testimonials = [
  {
    quote: "Their solution reduced our operational costs by 30% while improving efficiency!",
    author: "<PERSON>",
    role: "CEO, TechCorp",
    image: "/avatars/jane.jpg"
  },
  {
    quote: "The team's expertise in AI integration transformed our data processing capabilities.",
    author: "<PERSON>",
    role: "CTO, InnovateHub",
    image: "/avatars/john.jpg"
  },
  {
    quote: "Outstanding service and innovative solutions that truly made a difference.",
    author: "<PERSON>",
    role: "Director of Innovation, FutureTech",
    image: "/avatars/sarah.jpg"
  },
]

export function Testimonials() {
  return (
    <section className="py-24 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">What Our Clients Say</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Trusted by leading companies and organizations worldwide
          </p>
        </div>
        
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full max-w-5xl mx-auto">
          <CarouselContent>
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3 px-4">
                <Card>
                  <CardContent className="p-6">
                    <blockquote className="mb-6 text-lg">
                      "{testimonial.quote}"
                    </blockquote>
                    <div className="flex items-center gap-4">
                      <Avatar>
                        <AvatarImage src={testimonial.image} alt={testimonial.author} />
                        <AvatarFallback>{testimonial.author[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold">{testimonial.author}</div>
                        <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="hidden md:flex">
            <CarouselPrevious className="relative -left-4" />
            <CarouselNext className="relative -right-4" />
          </div>
        </Carousel>
      </div>
    </section>
  )
}