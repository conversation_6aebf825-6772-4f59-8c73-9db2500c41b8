import { compare, hash } from 'bcryptjs'
import { sign, verify } from 'jsonwebtoken'
import { prisma } from './prisma'

export interface AuthUser {
  id: string
  email: string
  name?: string | null
}

export async function hashPassword(password: string): Promise<string> {
  return hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return compare(password, hashedPassword)
}

export function generateToken(user: AuthUser): string {
  return sign(
    { userId: user.id },
    process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    { expiresIn: '7d' }
  )
}

export async function verifyToken(token: string): Promise<AuthUser | null> {
  try {
    const decoded = verify(
      token,
      process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
    ) as { userId: string }

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, email: true, name: true }
    })

    return user
  } catch (error) {
    return null
  }
}

export async function generatePasswordResetToken(email: string): Promise<string | null> {
  const user = await prisma.user.findUnique({ where: { email } })
  if (!user) return null

  const resetToken = Math.random().toString(36).slice(-8)
  const hashedResetToken = await hashPassword(resetToken)
  
  await prisma.user.update({
    where: { email },
    data: {
      resetToken: hashedResetToken,
      resetTokenExpiry: new Date(Date.now() + 3600000) // 1 hour from now
    }
  })

  return resetToken
}
