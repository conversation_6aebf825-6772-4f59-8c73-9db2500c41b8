'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/layout/header"
import { <PERSON>er } from "@/components/layout/footer"
import { ResponsiveImage } from "@/components/ui/responsive-image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { Separator } from "@/components/ui/separator"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

export default function ComponentsPage() {
  const [isLoading, setIsLoading] = useState(false)
  
  const toggleLoading = () => {
    setIsLoading(true)
    setTimeout(() => setIsLoading(false), 2000)
  }
  
  return (
    <>
      <Header />
      <main className="min-h-screen py-24">
        <div className="container mx-auto px-4">
          <div className="mb-12">
            <h1 className="text-4xl font-bold tracking-tight mb-4">Component Showcase</h1>
            <p className="text-xl text-muted-foreground">
              A demonstration of the UI components used throughout the site.
            </p>
          </div>
          
          <Tabs defaultValue="images">
            <TabsList className="mb-8">
              <TabsTrigger value="images">Responsive Images</TabsTrigger>
              <TabsTrigger value="buttons">Buttons</TabsTrigger>
              <TabsTrigger value="theme">Theme</TabsTrigger>
            </TabsList>
            
            <TabsContent value="images" className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Responsive Images</CardTitle>
                  <CardDescription>
                    Images that adapt to different screen sizes and handle loading states gracefully.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Standard Responsive Image</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      <ResponsiveImage
                        src="https://images.unsplash.com/photo-1498050108023-c5249f4df085"
                        alt="Person typing on a laptop with code on the screen"
                        width={800}
                        height={450}
                        aspectRatio="aspect-video"
                      />
                      <div>
                        <p className="mb-4">This image:</p>
                        <ul className="list-disc pl-6 space-y-2">
                          <li>Maintains a 16:9 aspect ratio</li>
                          <li>Shows a loading spinner while loading</li>
                          <li>Has a blur-up animation effect</li>
                          <li>Handles errors gracefully</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-lg font-medium mb-4">Different Aspect Ratios</h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div>
                        <p className="mb-2 font-medium">Square (1:1)</p>
                        <ResponsiveImage
                          src="https://images.unsplash.com/photo-1555066931-4365d14bab8c"
                          alt="Code on a computer screen"
                          width={400}
                          height={400}
                          aspectRatio="aspect-square"
                        />
                      </div>
                      <div>
                        <p className="mb-2 font-medium">Video (16:9)</p>
                        <ResponsiveImage
                          src="https://images.unsplash.com/photo-1517694712202-14dd9538aa97"
                          alt="Laptop with code"
                          width={640}
                          height={360}
                          aspectRatio="aspect-video"
                        />
                      </div>
                      <div>
                        <p className="mb-2 font-medium">Portrait (3:4)</p>
                        <ResponsiveImage
                          src="https://images.unsplash.com/photo-1555099962-4199c345e5dd"
                          alt="Vertical shot of code"
                          width={300}
                          height={400}
                          aspectRatio="aspect-[3/4]"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-lg font-medium mb-4">Error Handling</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      <ResponsiveImage
                        src="https://invalid-image-url.jpg"
                        alt="This image will fail to load"
                        width={800}
                        height={450}
                        aspectRatio="aspect-video"
                      />
                      <div>
                        <p className="mb-4">When an image fails to load:</p>
                        <ul className="list-disc pl-6 space-y-2">
                          <li>A placeholder is shown</li>
                          <li>The alt text is still available for screen readers</li>
                          <li>The layout doesn't break</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="buttons" className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Button Variants</CardTitle>
                  <CardDescription>
                    Different button styles for different contexts.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div className="flex flex-col items-center gap-2">
                      <Button variant="default">Default</Button>
                      <span className="text-sm text-muted-foreground">Default</span>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      <Button variant="destructive">Destructive</Button>
                      <span className="text-sm text-muted-foreground">Destructive</span>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      <Button variant="outline">Outline</Button>
                      <span className="text-sm text-muted-foreground">Outline</span>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      <Button variant="secondary">Secondary</Button>
                      <span className="text-sm text-muted-foreground">Secondary</span>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      <Button variant="ghost">Ghost</Button>
                      <span className="text-sm text-muted-foreground">Ghost</span>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      <Button variant="link">Link</Button>
                      <span className="text-sm text-muted-foreground">Link</span>
                    </div>
                  </div>
                  
                  <Separator className="my-8" />
                  
                  <h3 className="text-lg font-medium mb-4">Loading States</h3>
                  <div className="flex flex-wrap gap-4">
                    <Button onClick={toggleLoading} isLoading={isLoading}>
                      {!isLoading && "Click to Load"}
                    </Button>
                    <Button variant="outline" isLoading={isLoading}>
                      {!isLoading && "Loading Demo"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="theme" className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Theme Toggle</CardTitle>
                  <CardDescription>
                    Switch between light and dark mode.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center gap-4">
                    <ThemeToggle />
                    <p className="text-muted-foreground">
                      Click the sun/moon icon to toggle between light and dark mode.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </>
  )
}
