// File path: src/components/sections/hero-section.tsx
'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight, Globe, Code2, Lock } from "lucide-react"
import { ProgressiveBlur } from "@/components/ui/progressive-blur"
import { motion, useScroll } from "motion/react"
import { useEffect, useState } from "react"

export function HeroSection() {
  const [hasScrolled, setHasScrolled] = useState(false)
  const { scrollY } = useScroll()

  useEffect(() => {
    return scrollY.on("change", (latest) => {
      setHasScrolled(latest > 10)
    })
  }, [scrollY])

  return (
    <section className="relative py-28 md:py-36 overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 via-accent/10 to-background" />
        <div className="absolute top-20 left-1/2 -translate-x-1/2 w-[800px] h-[800px] rounded-full bg-primary/20 blur-3xl opacity-30" />
        <ProgressiveBlur />
      </div>

      <div className="container mx-auto px-4">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="mb-8 inline-flex items-center justify-center gap-2 text-sm bg-accent/10 px-4 py-2 rounded-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Lock className="h-4 w-4 text-accent" />
            <span className="text-accent-foreground font-medium">Trusted by leading companies worldwide</span>
          </motion.div>

          <motion.h1
            className="text-5xl font-bold tracking-tight sm:text-7xl mb-8 bg-clip-text text-transparent bg-gradient-to-r from-primary via-accent to-primary"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            Innovating Through Code and Collaboration
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-muted-foreground mb-10 max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            We build cutting-edge solutions while offering expert services to empower your vision.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-5 justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Button asChild variant="gradient" size="xl" className="gap-2">
              <Link href="/products">
                Explore Our Products
                <ArrowRight className="h-5 w-5" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="xl" className="gap-2">
              <Link href="/services">
                Browse Services
                <Globe className="h-5 w-5" />
              </Link>
            </Button>
          </motion.div>

          <motion.div
            className="mt-20 grid grid-cols-1 sm:grid-cols-3 gap-8 text-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring" }}
              className="bg-card/50 backdrop-blur-sm p-6 rounded-xl border shadow-sm"
            >
              <div className="font-bold text-3xl text-primary mb-2">50+</div>
              <div className="text-muted-foreground">Projects Delivered</div>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring" }}
              className="bg-card/50 backdrop-blur-sm p-6 rounded-xl border shadow-sm"
            >
              <div className="font-bold text-3xl text-primary mb-2">24/7</div>
              <div className="text-muted-foreground">Support Available</div>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring" }}
              className="bg-card/50 backdrop-blur-sm p-6 rounded-xl border shadow-sm"
            >
              <div className="font-bold text-3xl text-primary mb-2">100%</div>
              <div className="text-muted-foreground">Client Satisfaction</div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}