import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Check } from "lucide-react"
import Link from "next/link"

interface ServiceCardProps {
  service: {
    title: string
    description: string
    benefits: string[]
    icon: React.ComponentType<{ className?: string }>
  }
}

export function ServiceCard({ service }: ServiceCardProps) {
  return (
    <Card className="flex flex-col h-full">
      <CardHeader>
        <service.icon className="h-10 w-10 text-primary mb-4" />
        <CardTitle>{service.title}</CardTitle>
        <CardDescription>{service.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <ul className="space-y-2">
          {service.benefits.map((benefit) => (
            <li key={benefit} className="flex items-start gap-2">
              <Check className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
              <span className="text-muted-foreground">{benefit}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full">
          <Link href="/services/quote">Get a Quote</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
