'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Bo<PERSON>, Send, Loader2 } from "lucide-react"

type Message = {
  role: 'user' | 'assistant'
  content: string
}

const questions = [
  "What's your project scope?",
  "What's your expected timeline?",
  "What's your budget range?",
  "What specific features do you need?",
  "Do you have any technical requirements?",
]

export default function QuotePage() {
  const [messages, setMessages] = useState<Message[]>([
    { role: 'assistant', content: questions[0] }
  ])
  const [input, setInput] = useState('')
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!input.trim()) return

    // Add user's message
    const newMessages = [...messages, { role: 'user' as const, content: input }]
    setMessages(newMessages)
    setInput('')

    // If we haven't asked all questions yet
    if (currentQuestion < questions.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1)
        setMessages([...newMessages, {
          role: 'assistant',
          content: questions[currentQuestion + 1]
        }])
      }, 500)
    } else if (currentQuestion === questions.length - 1) {
      // Generate proposal
      setIsGenerating(true)
      setTimeout(() => {
        setMessages([...newMessages, {
          role: 'assistant',
          content: `Based on your requirements, here's a custom proposal:

• Full-stack web application development
• Expected timeline: 8-10 weeks
• Key features:
  - User authentication system
  - Real-time data processing
  - API integration
  - Mobile-responsive design
  - Analytics dashboard

Next steps: Let's schedule a detailed discussion to refine these specifications and discuss pricing options.`
        }])
        setIsGenerating(false)
      }, 2000)
    }
  }

  return (
    <main className="min-h-screen py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Get a Custom Quote</CardTitle>
              <CardDescription>
                Chat with our AI assistant to get a tailored proposal for your project
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-4">
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={`flex gap-3 ${
                      message.role === 'assistant' ? 'flex-row' : 'flex-row-reverse'
                    }`}
                  >
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      {message.role === 'assistant' ? (
                        <Bot className="w-5 h-5 text-primary" />
                      ) : (
                        <div className="w-5 h-5 rounded-full bg-zinc-600" />
                      )}
                    </div>
                    <div
                      className={`rounded-lg px-4 py-2 max-w-[80%] ${
                        message.role === 'assistant'
                          ? 'bg-muted'
                          : 'bg-primary text-primary-foreground ml-auto'
                      }`}
                    >
                      {message.content}
                    </div>
                  </div>
                ))}
                {isGenerating && (
                  <div className="flex gap-3">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Bot className="w-5 h-5 text-primary" />
                    </div>
                    <div className="rounded-lg px-4 py-2 bg-muted">
                      <Loader2 className="w-4 h-4 animate-spin" />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <form onSubmit={handleSubmit} className="flex gap-4 w-full">
                <Input
                  placeholder="Type your message..."
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  disabled={isGenerating}
                />
                <Button type="submit" isLoading={isGenerating}>
                  {!isGenerating && <Send className="w-4 h-4" />}
                </Button>
              </form>
            </CardFooter>
          </Card>
        </div>
      </div>
    </main>
  )
}
