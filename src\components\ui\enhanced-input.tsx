"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Eye, EyeOff, Search, X } from "lucide-react"

const inputVariants = cva(
  "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-all outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
  {
    variants: {
      variant: {
        default: "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        filled: "bg-muted/50 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        outline: "border-2 focus-visible:border-ring focus-visible:ring-ring/30 focus-visible:ring-[2px]",
        ghost: "border-none shadow-none focus-visible:ring-ring/30 focus-visible:ring-[2px]",
      },
      size: {
        default: "h-9 px-3 py-1",
        sm: "h-8 px-3 py-1 text-xs",
        lg: "h-11 px-4 py-2 text-base rounded-lg",
        xl: "h-12 px-5 py-2 text-lg rounded-lg",
      },
      radius: {
        default: "rounded-md",
        sm: "rounded",
        lg: "rounded-lg",
        xl: "rounded-xl",
        full: "rounded-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      radius: "default",
    },
  },
)

export interface EnhancedInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof inputVariants> {
  icon?: React.ReactNode
  clearable?: boolean
  onClear?: () => void
  iconPosition?: "left" | "right"
  showPasswordToggle?: boolean
}

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({ 
    className, 
    type = "text", 
    variant, 
    size, 
    radius,
    icon,
    clearable = false,
    onClear,
    iconPosition = "left",
    showPasswordToggle = false,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const [inputValue, setInputValue] = React.useState(props.value || props.defaultValue || "")
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value)
      props.onChange?.(e)
    }
    
    const handleClear = () => {
      setInputValue("")
      onClear?.()
      
      // Create a synthetic event to trigger onChange
      const input = document.createElement("input")
      input.value = ""
      const event = new Event("change", { bubbles: true })
      input.dispatchEvent(event)
      props.onChange?.(event as unknown as React.ChangeEvent<HTMLInputElement>)
    }
    
    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }
    
    // Determine the actual input type
    const inputType = showPasswordToggle 
      ? (showPassword ? "text" : "password") 
      : type
    
    // Determine if we should show the clear button
    const showClear = clearable && inputValue && String(inputValue).length > 0
    
    // Default icon based on input type
    let defaultIcon = icon
    if (!icon) {
      if (type === "search") defaultIcon = <Search className="h-4 w-4 text-muted-foreground" />
    }
    
    // Determine padding based on icon presence
    const leftPadding = iconPosition === "left" && (defaultIcon || (showPasswordToggle && type === "password")) 
      ? "pl-9" 
      : ""
    const rightPadding = (iconPosition === "right" && defaultIcon) || showClear || (showPasswordToggle && type === "password") 
      ? "pr-9" 
      : ""
    
    return (
      <div className="relative">
        {iconPosition === "left" && defaultIcon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none">
            {defaultIcon}
          </div>
        )}
        
        <input
          type={inputType}
          data-slot="input"
          className={cn(
            inputVariants({ variant, size, radius }),
            leftPadding,
            rightPadding,
            className
          )}
          ref={ref}
          value={inputValue}
          onChange={handleChange}
          {...props}
        />
        
        {iconPosition === "right" && defaultIcon && !showClear && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
            {defaultIcon}
          </div>
        )}
        
        {showClear && (
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            onClick={handleClear}
            tabIndex={-1}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Clear input</span>
          </button>
        )}
        
        {showPasswordToggle && type === "password" && (
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            onClick={togglePasswordVisibility}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
            <span className="sr-only">
              {showPassword ? "Hide password" : "Show password"}
            </span>
          </button>
        )}
      </div>
    )
  }
)

EnhancedInput.displayName = "EnhancedInput"

export { EnhancedInput, inputVariants }
