// File path: src/components/sections/featured-projects.tsx
'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ProjectCardSkeleton } from "@/components/ui/skeleton"
import Link from "next/link"
import { useEffect, useState } from "react"
import { motion } from "framer-motion"

const projects = [
  {
    name: "Project Alpha",
    description: "AI-powered analytics platform",
    status: "completed",
    progress: 100,
  },
  {
    name: "Project Beta",
    description: "Next-gen collaboration tools",
    status: "in_progress",
    progress: 75,
    launchDate: new Date('2025-06-20'),
  },
  // Add more projects
]

export function FeaturedProjects() {
  const [isLoading, setIsLoading] = useState(true)

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <section className="py-24 bg-slate-50">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold tracking-tight mb-12">Featured Projects</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {isLoading ? (
            <>
              {Array.from({ length: 3 }).map((_, index) => (
                <ProjectCardSkeleton key={index} />
              ))}
            </>
          ) : (
            <>
              {projects.map((project, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="flex flex-col h-full">
                    <CardHeader>
                      <CardTitle>{project.name}</CardTitle>
                      <p className="text-muted-foreground">{project.description}</p>
                    </CardHeader>
                    <CardContent className="flex-grow">
                      {project.status === 'completed' ? (
                        <div className="flex items-center text-green-600">
                          <span>Completed ✅</span>
                          <Button variant="link" className="ml-2">Download</Button>
                        </div>
                      ) : (
                        <div>
                          <div className="mb-2">In Development 🚧</div>
                          <div className="space-y-2">
                            <div className="text-sm text-muted-foreground">Progress: {project.progress}%</div>
                            <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                              <motion.div 
                                className="h-full bg-primary rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${project.progress}%` }}
                                transition={{ duration: 0.8, ease: "easeOut" }}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter>
                      <Button asChild variant="outline" className="w-full">
                        <Link href={`/projects/${project.name.toLowerCase()}`}>
                          Learn More
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </>
          )}
        </div>
        <div className="text-center">
          <Button asChild size="lg">
            <Link href="/projects">View All Projects</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}