'use client'

import { ImageIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PlaceholderImageProps {
  className?: string
  iconClassName?: string
}

export function PlaceholderImage({
  className,
  iconClassName
}: PlaceholderImageProps) {
  return (
    <div 
      className={cn(
        'flex items-center justify-center bg-muted/30 w-full h-full',
        className
      )}
    >
      <ImageIcon 
        className={cn(
          'text-muted-foreground/50 w-1/4 h-1/4',
          iconClassName
        )} 
      />
    </div>
  )
}
