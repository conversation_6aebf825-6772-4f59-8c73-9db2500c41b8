'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { MessageCircle, X } from "lucide-react"
import { useState } from "react"
import { ChatbotDialog } from "./chatbot-dialog"

export function ChatbotButton() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button
        className="fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg"
        onClick={() => setIsOpen(true)}
      >
        <MessageCircle className="h-6 w-6" />
        <span className="sr-only">Open chat</span>
      </Button>

      <ChatbotDialog open={isOpen} onOpenChange={setIsOpen} />
    </>
  )
}
