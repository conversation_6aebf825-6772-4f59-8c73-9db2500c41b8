interface LoadingSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "default" | "lg"
}

export function LoadingSpinner({ 
  size = "default", 
  className,
  ...props 
}: LoadingSpinnerProps) {
  const sizeClass = {
    sm: "h-4 w-4",
    default: "h-5 w-5",
    lg: "h-6 w-6"
  }[size]

  return (
    <div
      role="status"
      className="animate-spin"
      {...props}
    >
      <div className={`rounded-full border-2 border-background border-t-foreground ${sizeClass} ${className}`} />
      <span className="sr-only">Loading...</span>
    </div>
  )
}
