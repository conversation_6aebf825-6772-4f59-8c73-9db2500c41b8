// File path: src/app/services/page.tsx
import { ServiceCard } from "@/components/services/service-card"
import { Button } from "@/components/ui/button"
import { Code2, Database, Cloud, Cpu, LineChart, Headphones } from "lucide-react"
import Link from "next/link"

const serviceCategories = [
  {
    title: "Development",
    services: [
      {
        title: "Fullstack Development",
        description: "Scalable web applications tailored to your business needs, built with modern technologies and best practices.",
        benefits: [
          "24/7 Technical Support",
          "Scalable Architecture",
          "Regular Updates & Maintenance",
          "Performance Optimization"
        ],
        icon: Code2,
      },
      {
        title: "Database Engineering",
        description: "Robust database solutions designed for performance, security, and scalability.",
        benefits: [
          "Data Migration Support",
          "Performance Tuning",
          "Backup & Recovery",
          "Security Audits"
        ],
        icon: Database,
      },
    ],
  },
  {
    title: "AI & Machine Learning",
    services: [
      {
        title: "AI Integration",
        description: "Integrate cutting-edge AI capabilities into your existing systems and workflows.",
        benefits: [
          "Custom Model Development",
          "API Integration",
          "Performance Monitoring",
          "Regular Model Updates"
        ],
        icon: Cpu,
      },
      {
        title: "Data Analytics",
        description: "Transform your raw data into actionable insights with advanced analytics solutions.",
        benefits: [
          "Real-time Analytics",
          "Custom Dashboards",
          "Predictive Models",
          "Data Visualization"
        ],
        icon: LineChart,
      },
    ],
  },
  {
    title: "Consulting",
    services: [
      {
        title: "Technical Consulting",
        description: "Expert guidance on technology decisions, architecture, and implementation strategies.",
        benefits: [
          "Technology Assessment",
          "Architecture Review",
          "Security Audits",
          "Performance Optimization"
        ],
        icon: Cloud,
      },
      {
        title: "Support & Maintenance",
        description: "Comprehensive support to keep your systems running smoothly and efficiently.",
        benefits: [
          "24/7 Support",
          "Proactive Monitoring",
          "Regular Updates",
          "Security Patches"
        ],
        icon: Headphones,
      },
    ],
  },
]

export default function ServicesPage() {
  return (
    <main className="min-h-screen py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight mb-6">
            Our Services
          </h1>
          <p className="text-xl text-muted-foreground">
            Beyond our own projects, we offer expertise to bring your ideas to life.
            From concept to deployment, we're here to help you succeed.
          </p>
        </div>

        <div className="space-y-16">
          {serviceCategories.map((category) => (
            <section key={category.title}>
              <h2 className="text-2xl font-semibold mb-8">{category.title}</h2>
              <div className="grid md:grid-cols-2 gap-8">
                {category.services.map((service) => (
                  <ServiceCard key={service.title} service={service} />
                ))}
              </div>
            </section>
          ))}
        </div>

        <div className="mt-16 text-center">
          <Button asChild size="lg">
            <Link href="/services/quote">
              Get a Custom Quote
            </Link>
          </Button>
        </div>
      </div>
    </main>
  )
}