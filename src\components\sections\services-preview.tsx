// File path: src/components/sections/services-preview.tsx
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Code2, Database, Cloud, Cpu } from "lucide-react"

const services = [
  {
    title: "Fullstack Development",
    description: "End-to-end web and mobile solutions built with cutting-edge technologies. We deliver scalable applications that drive business growth.",
    icon: Code2,
  },
  {
    title: "AI Integration",
    description: "Leverage the power of artificial intelligence to automate processes and gain valuable insights. Custom AI solutions for your specific needs.",
    icon: Cpu,
  },
  {
    title: "Cloud Solutions",
    description: "Cloud-native architectures and migration services to modernize your infrastructure. Optimize performance and reduce costs.",
    icon: Cloud,
  },
  {
    title: "Database Engineering",
    description: "Design and implement robust database solutions. From data modeling to optimization and maintenance.",
    icon: Database,
  },
]

export function ServicesPreview() {
  return (
    <section className="py-24">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Our Services</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Comprehensive technology solutions to power your business
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {services.map((service, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <service.icon className="w-12 h-12 text-primary mb-4" />
                <CardTitle>{service.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{service.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button asChild size="lg">
            <Link href="/services">See All Services</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}