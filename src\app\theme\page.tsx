'use client'

import { useState, useEffect } from 'react'
import { useTheme } from 'next-themes'
import { Head<PERSON> } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { Separator } from "@/components/ui/separator"
import { Moon, Sun, Monitor, Check } from 'lucide-react'

export default function ThemePage() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  
  // Avoid hydration mismatch by only rendering theme components after mount
  useEffect(() => {
    setMounted(true)
  }, [])
  
  if (!mounted) return null
  
  return (
    <>
      <Header />
      <main className="min-h-screen py-24">
        <div className="container mx-auto px-4">
          <div className="mb-12">
            <h1 className="text-4xl font-bold tracking-tight mb-4">Theme Settings</h1>
            <p className="text-xl text-muted-foreground">
              Customize the appearance of the website to your preference.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Theme Selector</CardTitle>
                <CardDescription>
                  Choose between light, dark, or system theme.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    variant={theme === 'light' ? 'default' : 'outline'}
                    className="flex flex-col items-center justify-center gap-2 h-auto py-4"
                    onClick={() => setTheme('light')}
                  >
                    <Sun className="h-6 w-6" />
                    <span>Light</span>
                    {theme === 'light' && <Check className="h-4 w-4 absolute top-2 right-2" />}
                  </Button>
                  
                  <Button
                    variant={theme === 'dark' ? 'default' : 'outline'}
                    className="flex flex-col items-center justify-center gap-2 h-auto py-4"
                    onClick={() => setTheme('dark')}
                  >
                    <Moon className="h-6 w-6" />
                    <span>Dark</span>
                    {theme === 'dark' && <Check className="h-4 w-4 absolute top-2 right-2" />}
                  </Button>
                  
                  <Button
                    variant={theme === 'system' ? 'default' : 'outline'}
                    className="flex flex-col items-center justify-center gap-2 h-auto py-4"
                    onClick={() => setTheme('system')}
                  >
                    <Monitor className="h-6 w-6" />
                    <span>System</span>
                    {theme === 'system' && <Check className="h-4 w-4 absolute top-2 right-2" />}
                  </Button>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <p className="text-sm text-muted-foreground">
                  Current theme: <span className="font-medium capitalize">{theme}</span>
                </p>
                <ThemeToggle />
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Theme Preview</CardTitle>
                <CardDescription>
                  See how different components look in the current theme.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Buttons</h3>
                  <div className="flex flex-wrap gap-2">
                    <Button variant="default">Default</Button>
                    <Button variant="secondary">Secondary</Button>
                    <Button variant="outline">Outline</Button>
                    <Button variant="ghost">Ghost</Button>
                    <Button variant="destructive">Destructive</Button>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Cards</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <Card>
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm">Card Title</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0 text-xs">
                        Card content goes here.
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm">Card Title</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0 text-xs">
                        Card content goes here.
                      </CardContent>
                    </Card>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Tabs</h3>
                  <Tabs defaultValue="tab1">
                    <TabsList>
                      <TabsTrigger value="tab1">Tab 1</TabsTrigger>
                      <TabsTrigger value="tab2">Tab 2</TabsTrigger>
                    </TabsList>
                    <TabsContent value="tab1" className="p-2 text-sm">
                      Content for tab 1
                    </TabsContent>
                    <TabsContent value="tab2" className="p-2 text-sm">
                      Content for tab 2
                    </TabsContent>
                  </Tabs>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle>Theme Implementation</CardTitle>
                <CardDescription>
                  How the theme system works in this application
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  The theme system is implemented using:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>next-themes:</strong> A library that provides theme management for Next.js applications.
                  </li>
                  <li>
                    <strong>CSS Variables:</strong> Theme colors are defined as CSS variables in the globals.css file.
                  </li>
                  <li>
                    <strong>Tailwind CSS:</strong> The theme colors are integrated with Tailwind's configuration.
                  </li>
                  <li>
                    <strong>Local Storage:</strong> Your theme preference is saved in local storage for persistence.
                  </li>
                </ul>
                <p className="mt-4 text-sm text-muted-foreground">
                  The theme toggle in the header allows you to quickly switch between light and dark modes.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
